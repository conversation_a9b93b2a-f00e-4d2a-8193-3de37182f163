import youhuiquan from '@/assets/images/signInCampaign/youhuiquan.png'
import choujiang from '@/assets/images/signInCampaign/choujiang.png'
import jifen from '@/assets/images/signInCampaign/jifen.png'

type IData = {
  data:{
    list: any[]
    count: number
  }
}

/**
 * 我的奖励-hooks工具函数
 * @returns
 */
export const useRefreshAndLoadPagination = () => {
  // const { isXcx } = useAppConfig()
// /kukemarketing/xxx/punchTheClock/myRewardListProt
  const path = '/kukemarketing/wap/punchTheClock/myRewardListProt'
  const body = {
    // 打卡活动id
    id: '1131356562807455744',
    page: 1,
    pageSize: 10
  }
  const myRewardListProt = async () => {
    const { data } = await useHttp<IData>(path, {
      body
    })
    const { list, count, code } = data.value?.data as any || {}
    const _list = transformList(list)
    return { list: _list, count, isError: code !== '10000' }
  }
  return {
    myRewardListProt
  }
}

/**
 * [AI-GEN] 转优惠券标题
 * ```
 * <template v-if="coupon.activityType===1">
          <template v-if="coupon.couponThreshold===0">
            无门槛减{{ coupon.rewardAmount }}元
          </template>
          <template v-else>
            满{{ coupon.couponThreshold }}元减{{ coupon.rewardAmount }}元
          </template>
        </template>
        <template v-else-if="coupon.activityType===2">
          <template v-if="coupon.couponThreshold===0">
            无门槛享受{{ coupon.discountRate }}折
          </template>
          <template v-else>
            满{{ coupon.couponThreshold }}元享{{ coupon.discountRate }}折
            <template v-if="coupon.discountAmount">
              ,最多优惠{{ coupon.discountAmount }}元
            </template>
          </template>
        </template>
 * ```
 * @param coupon
 * @returns
 */
export function getCouponText (coupon = {} as any) {
  if (coupon.activityType === 1) {
    if (coupon.couponThreshold === 0) {
      return `无门槛减${coupon.rewardAmount}元`
    } else {
      return `满${coupon.couponThreshold}元减${coupon.rewardAmount}元`
    }
  } else if (coupon.activityType === 2) {
    if (coupon.couponThreshold === 0) {
      return `无门槛享受${coupon.discountRate}折`
    } else {
      let text = `满${coupon.couponThreshold}元享${coupon.discountRate}折`
      if (coupon.discountAmount) {
        text += `,最多优惠${coupon.discountAmount}元`
      }
      return text
    }
  }
  return '' // 默认返回空字符串
}

export const transformList = (list: any[]) => {
  return list.map((v) => {
    const {
      // 奖品类型 （1、优惠券 2、抽奖 3、商品 4、自定义奖品 5、任务商品 6、积分）
      prizeType,
      customInfo = {},
      pointInfo = {},
      couponInfo = {},
      // lotteryInfo = {},
      goodsInfo = {},
      awardType,
      awardsDay,
    } = v || {}
    v._img = ''
    if (awardType === 1) {
      v._desc = `连续签到${awardsDay}天奖励`
    } else if (awardType === 2) {
      v._desc = `累计签到${awardsDay}天奖励`
    } else {
      v._desc = `签到${awardsDay}天奖励`
    }
    v._desc2 = ''
    if (prizeType === 3) {
      v._title = goodsInfo.goodsTitle
      v._img = goodsInfo.goodsImg
      // v._desc = '连续签到X天奖励/累计签到X天奖励'
      // v._desc2 = '格式：年-月-日 时:分:秒'
    } else if (prizeType === 1) {
      v._img = youhuiquan
      v._title = getCouponText(couponInfo)

      // v._desc = '连续签到X天奖励/累计签到X天奖励'
      // v._desc2 = '格式：年-月-日 时:分:秒'
    } else if (prizeType === 2) {
      v._img = choujiang
      v._title = '1次免费抽奖机会'
      // v._desc = '连续签到X天奖励/累计签到X天奖励'
      // v._desc2 = '格式：年-月-日 时:分:秒'
    } else if (prizeType === 6) {
      v._img = jifen
      v._title = `${pointInfo.points}积分 （积分名称是变量，遵循运营后台积分名称配置进行展示）`
      // v._desc = '连续签到X天奖励/累计签到X天奖励'
      // v._desc2 = '格式：年-月-日 时:分:秒'
    } else if (prizeType === 4) {
      v._img = customInfo.awardImg
      v._title = customInfo.awardName
      // v._desc = '连续签到X天奖励/累计签到X天奖励'
      // v._desc2 = '格式：年-月-日 时:分:秒'
    }
    return v
  })
}
