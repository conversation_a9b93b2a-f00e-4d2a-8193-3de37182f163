<!-- 签到打卡活动排行榜 -->
<template>
  <div
    class="page-ranking break-all"
    :class="{
      'is-sticky': stickyFlag,
    }"
  >
    <NavBar :content="'规则规则规则规则'" class="" title="签到打卡活动排行榜" right-text="规则">
      <template #config>
        <div class="text-[28px]">
          规则
        </div>
      </template>
    </NavBar>
    <!-- <NavBar :content="ruleContent" class="" show-config title="签到打卡活动排行榜" right-text="规则" /> -->

    <!-- 自定义 Tab 切换 -->
    <div class="custom-tabs sticky top-[80px] z-20">
      <!-- :class="{
        'bg-white': stickyFlag,
      }" -->
      <!-- Tab 头部 -->
      <div class="tab-header">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          class="tab-item"
          :class="{ active: activeTab === index }"
          @click="onTabChange(index)"
        >
          {{ tab.title }}
        </div>
      </div>
    </div>

    <!-- 排行榜列表 -->
    <van-pull-refresh v-model="currentTabData.refreshing" class="ranking-list-container" @refresh="onRefresh">
      <van-list
        v-model:loading="currentTabData.loading"
        :finished="currentTabData.finished"
        finished-text=""
        :immediate-check="false"
        class="ranking-list"
        @load="onLoad"
      >
        <div v-if="cumulativeRankListTop3.length" class="rankTop-wrapper">
          <div
            v-for="(item, index) in cumulativeRankListTop3"
            :key="item.userId"
            class="rankTop flex flex-col justify-center items-center relative"
            :class="`rankTop-${index + 1}`"
          >
            <div class="avatar relative border bottom-[4px] border-white rounded-[50%]">
              <img :src="item.photo" alt="" class="rounded-[50%] w-full h-full object-cover">
              <!-- <img
                src="https://img.yzcdn.cn/vant/cat.jpeg"
                alt=""
                class="rounded-[50%] w-full h-full object-cover"
              > -->
              <span class="avatar-num absolute top-[-25px] left-[50%] ml-[-20px]">
                <!-- <img src="@/assets/images/signInCampaign/rank__no-2.png" alt="" class="w-[40px] h-[40px] object-cover"> -->
              </span>
            </div>
            <div class="name line-clamp-1 mb-[8px]">
              {{ item.userName }}
            </div>
            <div class="desc line-clamp-1">
              {{ currentTab.unit2 }} {{ item.punchClockDays }} 天
            </div>
          </div>
          <!-- <div class="rankTop rankTop-2 flex flex-col justify-center items-center relative">
            <div
              class="avatar relative border bottom-[4px] border-white rounded-[50%]"
            >
              <img src="https://img.yzcdn.cn/vant/cat.jpeg" alt="" class="rounded-[50%] w-full h-full object-cover">
              <span class="absolute top-[-25px] left-[50%] ml-[-20px]">
                <img src="@/assets/images/signInCampaign/rank__no-2.png" alt="" class="w-[40px] h-[40px] object-cover">
              </span>
            </div>
            <div class="name line-clamp-1 mb-[8px]">
              {{ cumulativeRankMyRank?.userName }}
            </div>
            <div class="desc line-clamp-1">
              {{ currentTab.unit2 }} {{ cumulativeRankMyRank?.punchClockDays }} 天
            </div>
          </div>
          <div class="rankTop rankTop-1 flex flex-col justify-center items-center relative">
            <div
              class="avatar relative w-[88px] h-[88px] border bottom-[4px] border-white rounded-[50%]"
            >
              <img src="https://img.yzcdn.cn/vant/cat.jpeg" alt="" class="rounded-[50%] w-full h-full object-cover">
              <span class="absolute top-[-25px] left-[50%] ml-[-20px]">
                <img src="@/assets/images/signInCampaign/rank__no-1.png" alt="" class="w-[40px] h-[40px] object-cover">
              </span>
            </div>
            <div class="name line-clamp-1 mb-[8px]">
              {{ cumulativeRankMyRank?.userName }}
            </div>
            <div class="desc line-clamp-1">
              {{ currentTab.unit2 }} {{ cumulativeRankMyRank?.punchClockDays }} 天
            </div>
          </div>
          <div class="rankTop rankTop-3 flex flex-col justify-center items-center relative">
            <div
              class="avatar relative w-[88px] h-[88px] border bottom-[4px] border-white rounded-[50%]"
            >
              <img src="https://img.yzcdn.cn/vant/cat.jpeg" alt="" class="rounded-[50%] w-full h-full object-cover">
              <span class="absolute top-[-25px] left-[50%] ml-[-20px]">
                <img src="@/assets/images/signInCampaign/rank__no-3.png" alt="" class="w-[40px] h-[40px] object-cover">
              </span>
            </div>
            <div class="name line-clamp-1 mb-[8px]">
              {{ cumulativeRankMyRank?.userName }}
            </div>
            <div class="desc line-clamp-1">
              {{ currentTab.unit2 }} {{ cumulativeRankMyRank?.punchClockDays }} 天
            </div>
          </div> -->
        </div>
        <!-- 排行榜项目 -->
        <template v-for="item in currentTabData.list" :key="item.userId">
          <RankingItem :item="item" :tab="currentTab" />
        </template>

        <!-- {{ !currentTabData.loading }}
        {{ currentTabData.finished }} -->
        <!-- 空状态 -->
        <template v-if="!currentTabData.loading && currentTabData.finished && [0, 3].includes(currentTabData.count)">
          <div class="empty-state">
            <div class="empty-icon">
              <img src="@/assets/icon__data-empty.png" alt="">
            </div>
            <div class="empty-text">
              <template v-if="currentTabData.count === 0">
                暂无用户上榜哦，快去抢占第一名吧~
              </template>
              <template v-else>
                暂无更多排名哦～
              </template>
            </div>
          </div>
          <!-- <div
            v-else-if="currentTabData.count === 3"
            class="empty-state"
          >
            <div class="empty-icon">
              <img src="@/assets/icon__data-empty.png" alt="">
            </div>
            <div class="empty-text">
            </div>
          </div> -->
        </template>
      </van-list>
    </van-pull-refresh>
    <!-- 当前用户排名 -->
    <div
      v-if="cumulativeRankMyRank?.userId"
      class="userRankingItem fixed bottom-0 left-[50%] w-[750px] translate-x-[-50%]"
    >
      <!-- TODO 兼容ios 全面屏 -->
      <RankingItem
        v-memo="[cumulativeRankMyRank.punchClockDays]"
        :item="cumulativeRankMyRank"
        class="is-currentUser"
        :tab="currentTab"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// /sign-in-campaign/ranking
import { ref, reactive, computed, onMounted } from 'vue'
import { useClockInWxShareInfo } from './hooks/useClockIn'
import RankingItem from './components/RankingItem.vue'
// import { useContinuousRank } from './hooks/useRanking'
// import { NewApi } from '~/apis/news'
// import NavBar from './components/NavBar.vue'
const cumulativeRankMyRank = ref<IRankingItem>()
const cumulativeRankListTop3 = ref<IRankingItem[]>([])
// const {
//   // cumulativeRankListTop3,
//   // cumulativeRankMyRank,
//   // ruleContent,
//   cumulativeRank
// } = useContinuousRank('1131356562807455744')
onMounted(async () => {
  await nextTick()
  // await cumulativeRank()
})
// 页面元数据
definePageMeta({
  ignoreLearnTarget: true,
})

// Tab 配置
interface TabConfig {
  title: string
  unit: string
  unit2: string
  type: 'cumulative' | 'consecutive'
}

const tabs: TabConfig[] = [
  {
    title: '累计签到榜',
    unit: '天',
    unit2: '累计签到',
    type: 'cumulative'

  },
  {
    title: '连续签到榜',
    unit: '天',
    unit2: '连续签到',
    type: 'consecutive'
  }
]

// 排行榜数据项接口
interface IRankingItem {
  userId: number
  sorting: number
  userName: string
  photo: string
  // description: string
  punchClockDays: number
}

// Tab 数据状态接口
interface TabData {
  list: IRankingItem[]
  loading: boolean
  refreshing: boolean
  finished: boolean
  page: number
  hasMore: boolean
}
// 当前激活的 tab
const activeTab = ref(0)

// 当前 tab 配置
const currentTab = computed(() => tabs[activeTab.value])

// 当前 tab 数据
const currentTabData = computed(() => tabsData[activeTab.value])

// 当前用户排名数据
// const currentUserRankingItem = computed(() => {
//   return {
//     userId: 1,
//     sorting: 1,
//     userName: '用户1',
//     photo: 'https://img.yzcdn.cn/vant/cat.jpeg',
//     // description: '累计签到 100 天',
//     punchClockDays: 100
//   }
// })

// 每个 tab 的数据状态
const tabsData = reactive<TabData[]>(
  tabs.map(() => ({
    list: [],
    loading: false,
    refreshing: false,
    finished: false,
    page: 1,
    pageSize: 20,
    count: 0,
    hasMore: true
  }))
)

// [AI-GEN] Mock 数据生成函数
// const generateMockData = (page: number, tabType: 'cumulative' | 'consecutive'): IRankingItem[] => {
//   const pageSize = 20
//   const startIndex = (page - 1) * pageSize
//   const mockData: IRankingItem[] = []

//   // 生成头像 URL 列表
//   const avatars = [
//     'https://img.yzcdn.cn/vant/cat.jpeg',
//     'https://img.yzcdn.cn/vant/tree.jpg',
//     'https://fastly.jsdelivr.net/npm/@vant/assets/apple-1.jpeg',
//     'https://fastly.jsdelivr.net/npm/@vant/assets/apple-2.jpeg',
//     'https://fastly.jsdelivr.net/npm/@vant/assets/apple-3.jpeg'
//   ]

//   for (let i = 0; i < pageSize; i++) {
//     const rank = startIndex + i + 1
//     const baseScore = tabType === 'cumulative' ? 100 : 50
//     const randomScore = Math.floor(Math.random() * baseScore) + (baseScore - rank)

//     mockData.push({
//       userId: rank,
//       sorting: rank,
//       userName: `用户${rank.toString().padStart(3, '0')}`,
//       photo: avatars[i % avatars.length],
//       // description: tabType === 'cumulative' ? `累计签到 ${randomScore} 天` : `连续签到 ${randomScore} 天`,
//       punchClockDays: randomScore
//     })
//   }

//   return mockData
// }

// 模拟 API 请求
// const fetchRankingData = async (tabIndex: number, page: number): Promise<{ data: IRankingItem[], hasMore: boolean }> => {
//   // 模拟网络延迟
//   // await new Promise(resolve => setTimeout(resolve, 800))

//   const tabType = tabs[tabIndex].type
//   const {list:data, count} = await cumulativeRank(page)
//   // const data = generateMockData(page, tabType)

//   // 模拟分页结束条件（假设最多有 5 页数据）
//   // const hasMore = page < 5
//   const hasMore = page < count
//   console.log('[ { data, hasMore } ] >', { data, hasMore })
//   return { data, hasMore }
// }

// 加载数据 - 简化版本
const getList = async () => {
  const tabData = currentTabData.value
  console.log('[ tabData.loading ] >', tabData.loading)

  tabData.loading = true
  Loading(true)
  // const { error, data } = await useHttp(NewApi.newsList, {
  const { error, data } = await useHttp('/kukemarketing/wap/punchTheClock/continuousRank', {
    // const { error, data } = await useHttp('/kukemarketing/wap/punchTheClock/cumulativeRank', {
    method: 'post',
    body: {
      id: '1131356562807455744',
      // cateId:1,
      page: tabData.page,
      pageSize: tabData.pageSize
    },
  })
  if (!error.value) {
    cumulativeRankMyRank.value = data.value?.data.myRank

    //
    const list = data.value?.data.list || []
    //   const list = [
    //     ...data.value?.data.list,
    //     ...data.value?.data.list,
    //     ...data.value?.data.list,
    // ] || []
    if (tabData.page === 1) {
      const [one, two, three, ..._list] = list

      if (two) {
        cumulativeRankListTop3.value[0] = two
      }
      if (one) {
        cumulativeRankListTop3.value[1] = one
      }
      if (three) {
        cumulativeRankListTop3.value[2] = three
      }
      tabData.list = _list || []
    } else {
      tabData.list = tabData.list.concat(list || [])
    }
    const count = data.value?.data.count

    tabData.count = count
    // tabData.count = 12
    if (
      (tabData.list.length + 3) >= count || !tabData.list.length
    ) {
      tabData.finished = true
    } else {
      tabData.finished = false
      //
      tabData.page = tabData.page + 1
    }
  }
  tabData.loading = false
  Loading(false)
}
// Tab 切换事件 - 简化版本
const onTabChange = async (index: number) => {
  console.log('切换到 tab:', index)
  activeTab.value = index

  const tabData = tabsData[index]

  // 如果当前 tab 没有数据且不在加载中，则加载数据
  if (tabData.list.length === 0 && !tabData.loading) {
    console.log('tab 无数据，开始加载')
    // loadData(true)
    await onLoad()
  } else {
    console.log('tab 已有数据或正在加载中，跳过加载')
  }
  document.documentElement.scrollTop = 0
}

// 下拉刷新 - 简化版本
const onRefresh = async () => {
  console.log('下拉刷新当前 tab:', activeTab.value)
  const tabData = currentTabData.value

  // 重置状态
  tabData.refreshing = false
  tabData.page = 1
  tabData.loading = true
  // tabData.hasMore = true
  await onLoad()
}

// 上拉加载更多 - 简化版本
const onLoad = async () => {
  console.log('[ onLoad 当前 tab: ] >', activeTab.value)
  const tabData = currentTabData.value

  tabData.finished = true

  await getList()
}

// 打卡 -- 微信分享
const { setWxShareInfo } = useClockInWxShareInfo()

// 页面初始化
onMounted(async () => {
  await nextTick()
  // 加载第一个 tab 的数据
  // loadData(true)
  await getList()

  // layout/default.vue 延迟了200ms执行，所以这里需要延迟200ms执行
  setTimeout(() => {
    // if (isWeChat() || isXcx) {
    setWxShareInfo()
    // }
  }, 300)
})
const stickyFlag = ref(false)

/**
 * xxxxx
 */
const scrollFn = (e) => {
  const scrollTop = e.scrollTop || document.documentElement.scrollTop || document.body.scrollTop || 0
  scrollTop > 20 ? (stickyFlag.value = true) : (stickyFlag.value = false)
}
onMounted(() => {
  document.addEventListener('scroll', scrollFn)
})
onUnmounted(() => {
  document.removeEventListener('scroll', scrollFn)
})

</script>

<style lang="scss" scoped>
// [AI-GEN]
.page-ranking {
  position: relative;
  min-height: 100vh;
  background-color: #fff;
  background-image: url('~/assets/images/signInCampaign/ranking-ng.png');
  // background-image: url('~/assets/images/signInCampaign/rank-bg-1.png');
  background-repeat: no-repeat;
  // background-position: top center;
  background-position: center -84px;
  background-size: contain;
  padding-bottom: 112px;

  :deep(.center-con) {
    background-color: transparent !important;
  }

  &.is-sticky {

    :deep(.center-con),
    .custom-tabs {
      background-color: #fff !important;
    }
  }
}

// 自定义 Tab 样式
.custom-tabs {
  padding: 12px 0;

  .tab-header {
    width: 702px;
    margin: 0 auto;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 26px;
    padding: 4px;
    display: flex;
    // margin-top: 12px;
    // margin-bottom: 12px;
  }

  .tab-item {
    flex: 1;
    height: 64px;
    line-height: 64px;
    text-align: center;
    border-radius: 24px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 28px;
    color: #111111;
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      background-color: #fff;
    }

    &:hover {
      opacity: 0.8;
    }
  }
}

// 排行榜列表容器
.ranking-list-container {
  min-height: calc(100vh - 88px - 96px);
}

.ranking-list {
  // padding: 0 24px;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  // margin-bottom: 16px;
  // opacity: 0.6;
}

.empty-text {
  width: 532px;
  height: 34px;
  // font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28px;
  color: #666666;
  line-height: 34px;
  text-align: center;
  // font-style: normal;
  // text-transform: none;
}

.userRankingItem {
  // .ranking-item{

  // }
  padding-bottom: 24px;
  /*兼容 IOS<11.2*/
  padding-bottom: calc(constant(safe-area-inset-bottom) + 24px);
  /*兼容 IOS>11.2*/
  padding-bottom: calc(env(safe-area-inset-bottom) + 24px);
}

.rankTop-wrapper {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 12px;
  // background-image: url('~/assets/images/signInCampaign/rank-bg-2.png');
  background-position: bottom center;
  background-size: contain;
  background-repeat: no-repeat;
}

.rankTop {
  background-size: contain;
  background-repeat: no-repeat;

  .avatar {
    width: 72px;
    height: 72px;
    position: relative;
    margin-bottom: 6px;

    // margin-top: -80px;
    // img{
    //   border: 2px solid #fff;
    // }
    .avatar-num {
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      display: block;
      width: 40px;
      height: 40px;
    }
  }

  .name {
    // font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 28px;
    color: #111111;
    height: 34px;
    line-height: 34px;
  }

  .desc {
    // font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 22px;
    color: #111111;
    line-height: 28px;
  }

  &.rankTop-2 {
    width: 220px;
    height: calc(240px + 12px + 40px);
    // padding-top: 120px;
    background-image: url('~/assets/images/signInCampaign/rank__no-1-bg.png');
    background-position: bottom center;

    .avatar {
      width: 88px;
      height: 88px;
      margin-bottom: 21px;
    }

    .avatar-num {
      background-image: url('~/assets/images/signInCampaign/rank__no-1.png');
    }
  }

  &.rankTop-1 {
    width: 194px;
    height: calc(224px);
    //
    background-image: url('~/assets/images/signInCampaign/rank__no-2-bg.png');
    background-position: bottom center;

    .avatar {
      //
    }

    .avatar-num {
      background-image: url('~/assets/images/signInCampaign/rank__no-2.png');
    }
  }

  &.rankTop-3 {
    width: 194px;
    height: calc(224px);
    //
    background-image: url('~/assets/images/signInCampaign/rank__no-3-bg.png');
    background-position: bottom center;

    .avatar {
      //
    }

    .avatar-num {
      background-image: url('~/assets/images/signInCampaign/rank__no-3.png');
    }
  }
}
</style>
