<template>
  <li
    :class="[
      `type-${item.prizeType}`
    ]"
    class="flex"
  >
    <div class="flex-shrink-0 mr-[16px] rounded-[16px] overflow-hidden">
      <img
        class="w-[165px] aspect-[3/2] transition-transform"
        loading="lazy"
        :src="item._img"
        alt=""
      >
    </div>
    <div class="flex-1 flex flex-col">
      <div class="mb-[8px] line-clamp-1 text-[28px] leading-[34px] font-medium text-[#111]">
        {{ item._title }}
      </div>
      <div class="mb-[8px] text-[24px] leading-[30px] text-[#999]">
        {{ item._desc }}
        <!-- <template v-if="item.awardType ===1">
          连续签到{{ item.awardsDay }}天奖励
        </template>
        <template v-else-if="item.awardType ===2">
          累计签到{{ item.awardsDay }}天奖励
        </template> -->
      </div>
      <div class="mb-[8px] flex flex-wrap text-[24px] leading-[30px] text-[#999]">
        <div class="">
          <!-- {{ item._desc2 }} -->
          {{ item.createdAt }}
          <!-- 2024.06.31 18:20:00 -->
        </div>
      </div>
    </div>
    <div class="flex-shrink-0 flex flex-col ml-[16px] justify-center">
      <slot />
    </div>
  </li>
</template>

<script lang="ts" setup>
defineProps({
  item: {
    type: Object,
    default: () => {
      return {
        content: [],
      }
    }
  }
})
onMounted(async () => {
  await nextTick()
})
</script>

<style>
/*  */
</style>
