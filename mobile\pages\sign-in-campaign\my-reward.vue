<template>
  <div class="page-my-reward break-all">
    <LazySkeleton
      v-if="isXcx"
      v-show="loading2"
      class="fixed left-0 top-0 z-[99999999] w-[100%] h-[100%]"
    />
    <NavBar v-if="!isXcx && !isKukeCloudAppWebview" title="我的奖励" />

    <section class="mt-[24px] mx-[24px]">
      <van-pull-refresh
        v-if="resultList?.length > 0"
        v-model="refreshing"
        class="content-item"
        @refresh="onRefresh"
      >
        <!-- success-text="刷新成功" -->
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text=""
          :immediate-check="false"
          @load="onLoad"
        >
          <MyRewardItem
            v-for="(item, index) in resultList"
            :key="index"
            :item="item"
            class=""
          >
            <!-- {{ index + 1 }} -->
            <!-- 奖品类型 （1、优惠券 2、抽奖 3、商品 4、自定义奖品 5、任务商品 6、积分） -->
            <span
              v-if="item.prizeType === 1 && item.couponInfo.couponId"
              class="text-[#fff] px-[16px] h-[48px] rounded-[12px] bg-brand flex justify-center items-center"
            >
              <!--
            如果是全部商品可用，进入全部商品可用页面，示例测试链接
            /courseList/1?pageId=966178409666289664&pageType=100&isPageId=1

如果是部分类型可用，进入部分类型可用页面，示例测试链接
/user/coupon/1127133191245705216?assignTime=1757841301000&isPageId=1

如果是部分商品可用，进入部分商品可用页面，示例测试链接
/activity/couponGoods/1127133324775567360?assignTime=1757841269000

如果是部分商品不可用，进入部分商品不可用页面，示例测试链接
/user/coupon/1127133417750704128?assignTime=1757841258000&isPageId=1
            -->
              <!-- 品设置;  1:全部商品可用  2:部分类型可用，3:部分类型不可用，4:部分商品可用，5:部分商品不可用 -->
              <a
                v-if="item.couponInfo.goodsSetting ===1"
                :href="`/courseList/1`"
              >
                去使用
              </a>
              <a
                v-else-if="item.couponInfo.goodsSetting ===2"
                :href="`/user/coupon/${item.couponInfo.couponId}?assignTime=${Date.now()}&isPageId=1`"
              >
                去使用
              </a>
              <a
                v-else-if="item.couponInfo.goodsSetting ===3"
                :href="`/activity/couponGoods/${item.couponInfo.couponId}?assignTime=${Date.now()}`"
              >
                去使用
              </a>
              <a
                v-else-if="item.couponInfo.goodsSetting ===4"
                :href="`/user/coupon/${item.couponInfo.couponId}?assignTime=${Date.now()}&isPageId=1`"
              >
                去使用
              </a>
            </span>
            <a
              v-else-if="item.prizeType === 2"
              href="/"
              class="text-[#fff] px-[16px] h-[48px] rounded-[12px] bg-brand flex justify-center items-center"
            >
              <!--
                TODO
                点击此按钮进入抽奖页面 -->
              去抽奖
            </a>
            <span
              v-else-if="item.prizeType === 3"
              class="text-[#fff] px-[16px] h-[48px] rounded-[12px] bg-brand flex justify-center items-center"
            >
              <!--
            若是不需要邮寄的商品，展示去查看按钮，点击此按钮进入学习中心页面
              若此商品需要邮寄，额外展示去领取按钮，点击此按钮展示领取奖励弹窗；
              若已填完展示查看物流按钮，点击此按钮展示已有的查看物流页面 -->
              <a
                v-if="!item.isMail"
                href="/learn-center"
              >
                去查看
              </a>
              <template v-else>
                <a
                  v-if="item.isDeliverGoods"
                  :href="`/user/order/logistics?orderSn=${item.goodsInfo.orderNo || ''}&fromPage=points`"
                >
                  查看物流
                </a>
                <template v-else>
                  去领取
                </template>
              </template>
            </span>
            <span
              v-else-if="item.prizeType === 4"
              class="text-[#fff] px-[16px] h-[48px] rounded-[12px] bg-brand flex justify-center items-center"
            >
              <!--
            若此奖品需要邮寄，额外展示去领取按钮，点击此按钮展示领取奖励弹窗；
               若已填完展示查看物流按钮，点击此按钮展示已有的查看物流页面 -->
              <a
                v-if="item.isDeliverGoods"
                :href="`/user/order/logistics?orderSn=${item.customInfo.orderNo || ''}&fromPage=points`"
              >
                查看物流
              </a>
              <template v-else-if="item.isMail">
                去领取
              </template>
            </span>
            <a
              v-else-if="item.prizeType === 6"
              href="/points"
              class="text-[#fff] px-[16px] h-[48px] rounded-[12px] bg-brand flex justify-center items-center"
            >
              去使用
            </a>
            <span
              v-else-if="item.prizeType === 5"
              class="text-[#fff] px-[16px] h-[48px] rounded-[12px] bg-brand flex justify-center items-center"
            >
              查看物流
            </span>
          </MyRewardItem>
          <!-- <NewsListCell
            v-for="item in resultList"
            :key="item.id"
            :info="item"
          /> -->
        </van-list>
      </van-pull-refresh>
      <!-- 无搜索结果 -->
      <!-- <div v-else> -->
      <template v-if="!resultList?.length && finished">
        <KKCEmpty text="暂无奖励，快去打卡获得奖励吧" :img="'no-data'" svg />
      </template>
      <!-- </div> -->
    </section>
  </div>
</template>
<script lang="ts" setup>
// import { useRefreshAndLoadPagination } from './hooks/useRefreshAndLoadPagination'
import MyRewardItem from './components/MyRewardItem.vue'
// import { NewApi } from '~/apis/news'
import {
  // getCouponText,
  transformList
} from './hooks/useRefreshAndLoadPagination'
import type { NewInfoDTO } from '~/apis/news/types'

// const { myRewardListProt } = useRefreshAndLoadPagination('1131356562807455744')
onMounted(async () => {
  await nextTick()
  // await myRewardListProt()
})
definePageMeta({
  ignoreLearnTarget: true,
})
const route = useRoute()
// 全局app信息
const { isXcx, isKukeCloudAppWebview } = useAppConfig()
// 搜索条件
const params = ref({
  cateId: 1,
  id: '1131356562807455744',
  page: 1,
  pageSize: 20
})

// 搜索列表
const finished = ref<boolean>(false)
// const flag = ref<boolean>(true)
const refreshing = ref<boolean>(false)
const loading = ref<boolean>(false)

//
// const flag = ref<boolean>(true)
const resultList = ref<NewInfoDTO[]>([])

// 获取列表
const getList = async () => {
  loading.value = true
  //   flag.value = false
  Loading(true)
  //   if (refreshing.value) {
  //     // params.value.page = 1
  //     resultList.value = []
  //     refreshing.value = false
  //   }
  const { error, data } = await useHttp<{ data: any }>('/kukemarketing/wap/punchTheClock/myRewardListProt', {
    method: 'post',
    body: { ...params.value },

    // watch: false
  })
  if (!error.value) {
    //
    const list = data.value?.data?.list || []
    const _list = transformList(list)
    if (params.value.page === 1) {
      resultList.value = _list
    } else {
      resultList.value = resultList.value.concat(_list)
    }
    if (
      resultList.value?.length >= data.value?.data.count ||
      !resultList.value?.length
    ) {
      finished.value = true
    } else {
      finished.value = false
      // params.value.page++
      params.value.page = params.value.page + 1
    }
  }
  loading.value = false
  //   flag.value = true
  Loading(false)
  if (process.client) {
    loading2.value = false
  }
}
const onRefresh = async () => {
  refreshing.value = false
  params.value.page = 1
  //   清空列表数据
  //   finished.value = false
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true
  //   await initParams()
  await onLoad()
}
// 上拉加载
const onLoad = async () => {
  //   finished.value = true
  finished.value = true
  await getList()
  // params.value.page = params.value.page + 1
}
const loading2 = ref(true)
// 初始化
const initParams = async () => {
  params.value.page = 1
  await getList()
}

//

// const router = useRouter()

watch(
  () => route.query,
  () => {
    initParams()
  },
  { immediate: false }
)
onBeforeMount(() => {
  //
})
// await initParams()
onMounted(async () => {
  await nextTick()
  //
  await initParams()
})
</script>
<style lang="scss" scoped>
.content-item {
    // min-height: calc(100vh - 80px - 24px);
}
.page-my-reward {
  background-color: #fff;
    min-height: 100%;
}
</style>
<style lang="scss">
// body {
// min-height: 100vh;
// min-height: 100dvh;
// }
html,
body {
//   overflow-x: hidden;
//   overflow-x: initial;
// height: 100%
}
</style>
